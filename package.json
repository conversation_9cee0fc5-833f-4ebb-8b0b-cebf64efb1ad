{"name": "qeleme-webapp", "version": "0.1.0", "private": true, "description": "AI-powered educational platform for Ethiopian high school students", "author": "Qeleme Team", "license": "MIT", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "prepare": "husky"}, "dependencies": {"clsx": "^2.1.1", "next": "^15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "husky": "^9.1.7", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "lint-staged": "^16.1.2", "prettier": "^3.6.1", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}