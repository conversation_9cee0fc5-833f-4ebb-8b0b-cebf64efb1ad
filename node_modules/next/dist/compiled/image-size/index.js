(()=>{var t={457:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.detector=void 0;const r=n(229);const i=Object.keys(r.typeHandlers);const s={56:"psd",66:"bmp",68:"dds",71:"gif",73:"tiff",77:"tiff",82:"webp",105:"icns",137:"png",255:"jpg"};function detector(t){const e=t[0];if(e in s){const n=s[e];if(n&&r.typeHandlers[n].validate(t)){return n}}const finder=e=>r.typeHandlers[e].validate(t);return i.find(finder)}e.detector=detector},833:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.types=e.setConcurrency=e.disableTypes=e.disableFS=e.imageSize=void 0;const r=n(147);const i=n(17);const s=n(927);const o=n(229);const c=n(457);const a=512*1024;const u=new s.default({concurrency:100,autostart:true});const d={disabledFS:false,disabledTypes:[]};function lookup(t,e){const n=(0,c.detector)(t);if(typeof n!=="undefined"){if(d.disabledTypes.indexOf(n)>-1){throw new TypeError("disabled file type: "+n)}if(n in o.typeHandlers){const r=o.typeHandlers[n].calculate(t,e);if(r!==undefined){r.type=r.type??n;return r}}}throw new TypeError("unsupported file type: "+n+" (file: "+e+")")}async function readFileAsync(t){const e=await r.promises.open(t,"r");try{const{size:t}=await e.stat();if(t<=0){throw new Error("Empty file")}const n=Math.min(t,a);const r=new Uint8Array(n);await e.read(r,0,n,0);return r}finally{await e.close()}}function readFileSync(t){const e=r.openSync(t,"r");try{const{size:t}=r.fstatSync(e);if(t<=0){throw new Error("Empty file")}const n=Math.min(t,a);const i=new Uint8Array(n);r.readSync(e,i,0,n,0);return i}finally{r.closeSync(e)}}t.exports=e=imageSize;e["default"]=imageSize;function imageSize(t,e){if(t instanceof Uint8Array){return lookup(t)}if(typeof t!=="string"||d.disabledFS){throw new TypeError("invalid invocation. input should be a Uint8Array")}const n=i.resolve(t);if(typeof e==="function"){u.push((()=>readFileAsync(n).then((t=>process.nextTick(e,null,lookup(t,n)))).catch(e)))}else{const t=readFileSync(n);return lookup(t,n)}}e.imageSize=imageSize;const disableFS=t=>{d.disabledFS=t};e.disableFS=disableFS;const disableTypes=t=>{d.disabledTypes=t};e.disableTypes=disableTypes;const setConcurrency=t=>{u.concurrency=t};e.setConcurrency=setConcurrency;e.types=Object.keys(o.typeHandlers)},436:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.BMP=void 0;const r=n(779);e.BMP={validate:t=>(0,r.toUTF8String)(t,0,2)==="BM",calculate:t=>({height:Math.abs((0,r.readInt32LE)(t,22)),width:(0,r.readUInt32LE)(t,18)})}},67:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.CUR=void 0;const r=n(845);const i=n(779);const s=2;e.CUR={validate(t){const e=(0,i.readUInt16LE)(t,0);const n=(0,i.readUInt16LE)(t,4);if(e!==0||n===0)return false;const r=(0,i.readUInt16LE)(t,2);return r===s},calculate:t=>r.ICO.calculate(t)}},400:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.DDS=void 0;const r=n(779);e.DDS={validate:t=>(0,r.readUInt32LE)(t,0)===542327876,calculate:t=>({height:(0,r.readUInt32LE)(t,12),width:(0,r.readUInt32LE)(t,16)})}},197:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.GIF=void 0;const r=n(779);const i=/^GIF8[79]a/;e.GIF={validate:t=>i.test((0,r.toUTF8String)(t,0,6)),calculate:t=>({height:(0,r.readUInt16LE)(t,8),width:(0,r.readUInt16LE)(t,6)})}},974:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.HEIF=void 0;const r=n(779);const i={avif:"avif",mif1:"heif",msf1:"heif",heic:"heic",heix:"heic",hevc:"heic",hevx:"heic"};e.HEIF={validate(t){const e=(0,r.toUTF8String)(t,4,8);if(e!=="ftyp")return false;const n=(0,r.findBox)(t,"ftyp",0);if(!n)return false;const s=(0,r.toUTF8String)(t,n.offset+8,n.offset+12);return s in i},calculate(t){const e=(0,r.findBox)(t,"meta",0);const n=e&&(0,r.findBox)(t,"iprp",e.offset+12);const i=n&&(0,r.findBox)(t,"ipco",n.offset+8);const s=i&&(0,r.findBox)(t,"ispe",i.offset+8);if(s){return{height:(0,r.readUInt32BE)(t,s.offset+16),width:(0,r.readUInt32BE)(t,s.offset+12),type:(0,r.toUTF8String)(t,8,12)}}throw new TypeError("Invalid HEIF, no size found")}}},512:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.ICNS=void 0;const r=n(779);const i=4+4;const s=4;const o=4;const c={ICON:32,"ICN#":32,"icm#":16,icm4:16,icm8:16,"ics#":16,ics4:16,ics8:16,is32:16,s8mk:16,icp4:16,icl4:32,icl8:32,il32:32,l8mk:32,icp5:32,ic11:32,ich4:48,ich8:48,ih32:48,h8mk:48,icp6:64,ic12:32,it32:128,t8mk:128,ic07:128,ic08:256,ic13:256,ic09:512,ic14:512,ic10:1024};function readImageHeader(t,e){const n=e+o;return[(0,r.toUTF8String)(t,e,n),(0,r.readUInt32BE)(t,n)]}function getImageSize(t){const e=c[t];return{width:e,height:e,type:t}}e.ICNS={validate:t=>(0,r.toUTF8String)(t,0,4)==="icns",calculate(t){const e=t.length;const n=(0,r.readUInt32BE)(t,s);let o=i;let c=readImageHeader(t,o);let a=getImageSize(c[0]);o+=c[1];if(o===n)return a;const u={height:a.height,images:[a],width:a.width};while(o<n&&o<e){c=readImageHeader(t,o);a=getImageSize(c[0]);o+=c[1];u.images.push(a)}return u}}},845:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.ICO=void 0;const r=n(779);const i=1;const s=2+2+2;const o=1+1+1+1+2+2+4+4;function getSizeFromOffset(t,e){const n=t[e];return n===0?256:n}function getImageSize(t,e){const n=s+e*o;return{height:getSizeFromOffset(t,n+1),width:getSizeFromOffset(t,n)}}e.ICO={validate(t){const e=(0,r.readUInt16LE)(t,0);const n=(0,r.readUInt16LE)(t,4);if(e!==0||n===0)return false;const s=(0,r.readUInt16LE)(t,2);return s===i},calculate(t){const e=(0,r.readUInt16LE)(t,4);const n=getImageSize(t,0);if(e===1)return n;const i=[n];for(let n=1;n<e;n+=1){i.push(getImageSize(t,n))}return{height:n.height,images:i,width:n.width}}}},229:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.typeHandlers=void 0;const r=n(436);const i=n(67);const s=n(400);const o=n(197);const c=n(974);const a=n(512);const u=n(845);const d=n(885);const l=n(530);const f=n(934);const h=n(245);const p=n(33);const g=n(105);const y=n(210);const I=n(448);const v=n(136);const w=n(414);const E=n(753);const m=n(940);const U=n(226);e.typeHandlers={bmp:r.BMP,cur:i.CUR,dds:s.DDS,gif:o.GIF,heif:c.HEIF,icns:a.ICNS,ico:u.ICO,j2c:d.J2C,jp2:l.JP2,jpg:f.JPG,jxl:h.JXL,"jxl-stream":p.JXLStream,ktx:g.KTX,png:y.PNG,pnm:I.PNM,psd:v.PSD,svg:w.SVG,tga:E.TGA,tiff:m.TIFF,webp:U.WEBP}},885:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.J2C=void 0;const r=n(779);e.J2C={validate:t=>(0,r.readUInt32BE)(t,0)===4283432785,calculate:t=>({height:(0,r.readUInt32BE)(t,12),width:(0,r.readUInt32BE)(t,8)})}},530:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.JP2=void 0;const r=n(779);e.JP2={validate(t){const e=(0,r.toUTF8String)(t,4,8);if(e!=="jP  ")return false;const n=(0,r.findBox)(t,"ftyp",0);if(!n)return false;const i=(0,r.toUTF8String)(t,n.offset+8,n.offset+12);return i==="jp2 "},calculate(t){const e=(0,r.findBox)(t,"jp2h",0);const n=e&&(0,r.findBox)(t,"ihdr",e.offset+8);if(n){return{height:(0,r.readUInt32BE)(t,n.offset+8),width:(0,r.readUInt32BE)(t,n.offset+12)}}throw new TypeError("Unsupported JPEG 2000 format")}}},934:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.JPG=void 0;const r=n(779);const i="45786966";const s=2;const o=6;const c=2;const a="4d4d";const u="4949";const d=12;const l=2;function isEXIF(t){return(0,r.toHexString)(t,2,6)===i}function extractSize(t,e){return{height:(0,r.readUInt16BE)(t,e),width:(0,r.readUInt16BE)(t,e+2)}}function extractOrientation(t,e){const n=8;const i=o+n;const s=(0,r.readUInt)(t,16,i,e);for(let n=0;n<s;n++){const s=i+l+n*d;const o=s+d;if(s>t.length){return}const c=t.slice(s,o);const a=(0,r.readUInt)(c,16,0,e);if(a===274){const t=(0,r.readUInt)(c,16,2,e);if(t!==3){return}const n=(0,r.readUInt)(c,32,4,e);if(n!==1){return}return(0,r.readUInt)(c,16,8,e)}}}function validateExifBlock(t,e){const n=t.slice(s,e);const i=(0,r.toHexString)(n,o,o+c);const d=i===a;const l=i===u;if(d||l){return extractOrientation(n,d)}}function validateInput(t,e){if(e>t.length){throw new TypeError("Corrupt JPG, exceeded buffer limits")}}e.JPG={validate:t=>(0,r.toHexString)(t,0,2)==="ffd8",calculate(t){t=t.slice(4);let e;let n;while(t.length){const i=(0,r.readUInt16BE)(t,0);if(t[i]!==255){t=t.slice(1);continue}if(isEXIF(t)){e=validateExifBlock(t,i)}validateInput(t,i);n=t[i+1];if(n===192||n===193||n===194){const n=extractSize(t,i+5);if(!e){return n}return{height:n.height,orientation:e,width:n.width}}t=t.slice(i+2)}throw new TypeError("Invalid JPG, no size found")}}},33:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.JXLStream=void 0;const r=n(779);const i=n(112);function calculateImageDimension(t,e){if(e){return 8*(1+t.getBits(5))}else{const e=t.getBits(2);const n=[9,13,18,30][e];return 1+t.getBits(n)}}function calculateImageWidth(t,e,n,r){if(e&&n===0){return 8*(1+t.getBits(5))}else if(n===0){return calculateImageDimension(t,false)}else{const t=[1,1.2,4/3,1.5,16/9,5/4,2];return Math.floor(r*t[n-1])}}e.JXLStream={validate:t=>(0,r.toHexString)(t,0,2)==="ff0a",calculate(t){const e=new i.BitReader(t,"little-endian");const n=e.getBits(1)===1;const r=calculateImageDimension(e,n);const s=e.getBits(3);const o=calculateImageWidth(e,n,s,r);return{width:o,height:r}}}},245:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.JXL=void 0;const r=n(779);const i=n(33);function extractCodestream(t){const e=(0,r.findBox)(t,"jxlc",0);if(e){return t.slice(e.offset+8,e.offset+e.size)}const n=extractPartialStreams(t);if(n.length>0){return concatenateCodestreams(n)}return undefined}function extractPartialStreams(t){const e=[];let n=0;while(n<t.length){const i=(0,r.findBox)(t,"jxlp",n);if(!i)break;e.push(t.slice(i.offset+12,i.offset+i.size));n=i.offset+i.size}return e}function concatenateCodestreams(t){const e=t.reduce(((t,e)=>t+e.length),0);const n=new Uint8Array(e);let r=0;for(const e of t){n.set(e,r);r+=e.length}return n}e.JXL={validate:t=>{const e=(0,r.toUTF8String)(t,4,8);if(e!=="JXL ")return false;const n=(0,r.findBox)(t,"ftyp",0);if(!n)return false;const i=(0,r.toUTF8String)(t,n.offset+8,n.offset+12);return i==="jxl "},calculate(t){const e=extractCodestream(t);if(e)return i.JXLStream.calculate(e);throw new Error("No codestream found in JXL container")}}},105:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.KTX=void 0;const r=n(779);e.KTX={validate:t=>{const e=(0,r.toUTF8String)(t,1,7);return["KTX 11","KTX 20"].includes(e)},calculate:t=>{const e=t[5]===49?"ktx":"ktx2";const n=e==="ktx"?36:20;return{height:(0,r.readUInt32LE)(t,n+4),width:(0,r.readUInt32LE)(t,n),type:e}}}},210:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.PNG=void 0;const r=n(779);const i="PNG\r\n\n";const s="IHDR";const o="CgBI";e.PNG={validate(t){if(i===(0,r.toUTF8String)(t,1,8)){let e=(0,r.toUTF8String)(t,12,16);if(e===o){e=(0,r.toUTF8String)(t,28,32)}if(e!==s){throw new TypeError("Invalid PNG")}return true}return false},calculate(t){if((0,r.toUTF8String)(t,12,16)===o){return{height:(0,r.readUInt32BE)(t,36),width:(0,r.readUInt32BE)(t,32)}}return{height:(0,r.readUInt32BE)(t,20),width:(0,r.readUInt32BE)(t,16)}}}},448:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.PNM=void 0;const r=n(779);const i={P1:"pbm/ascii",P2:"pgm/ascii",P3:"ppm/ascii",P4:"pbm",P5:"pgm",P6:"ppm",P7:"pam",PF:"pfm"};const s={default:t=>{let e=[];while(t.length>0){const n=t.shift();if(n[0]==="#"){continue}e=n.split(" ");break}if(e.length===2){return{height:parseInt(e[1],10),width:parseInt(e[0],10)}}else{throw new TypeError("Invalid PNM")}},pam:t=>{const e={};while(t.length>0){const n=t.shift();if(n.length>16||n.charCodeAt(0)>128){continue}const[r,i]=n.split(" ");if(r&&i){e[r.toLowerCase()]=parseInt(i,10)}if(e.height&&e.width){break}}if(e.height&&e.width){return{height:e.height,width:e.width}}else{throw new TypeError("Invalid PAM")}}};e.PNM={validate:t=>(0,r.toUTF8String)(t,0,2)in i,calculate(t){const e=(0,r.toUTF8String)(t,0,2);const n=i[e];const o=(0,r.toUTF8String)(t,3).split(/[\r\n]+/);const c=s[n]||s.default;return c(o)}}},136:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.PSD=void 0;const r=n(779);e.PSD={validate:t=>(0,r.toUTF8String)(t,0,4)==="8BPS",calculate:t=>({height:(0,r.readUInt32BE)(t,14),width:(0,r.readUInt32BE)(t,18)})}},414:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.SVG=void 0;const r=n(779);const i=/<svg\s([^>"']|"[^"]*"|'[^']*')*>/;const s={height:/\sheight=(['"])([^%]+?)\1/,root:i,viewbox:/\sviewBox=(['"])(.+?)\1/i,width:/\swidth=(['"])([^%]+?)\1/};const o=2.54;const c={in:96,cm:96/o,em:16,ex:8,m:96/o*100,mm:96/o/10,pc:96/72/12,pt:96/72,px:1};const a=new RegExp(`^([0-9.]+(?:e\\d+)?)(${Object.keys(c).join("|")})?$`);function parseLength(t){const e=a.exec(t);if(!e){return undefined}return Math.round(Number(e[1])*(c[e[2]]||1))}function parseViewbox(t){const e=t.split(" ");return{height:parseLength(e[3]),width:parseLength(e[2])}}function parseAttributes(t){const e=t.match(s.width);const n=t.match(s.height);const r=t.match(s.viewbox);return{height:n&&parseLength(n[2]),viewbox:r&&parseViewbox(r[2]),width:e&&parseLength(e[2])}}function calculateByDimensions(t){return{height:t.height,width:t.width}}function calculateByViewbox(t,e){const n=e.width/e.height;if(t.width){return{height:Math.floor(t.width/n),width:t.width}}if(t.height){return{height:t.height,width:Math.floor(t.height*n)}}return{height:e.height,width:e.width}}e.SVG={validate:t=>i.test((0,r.toUTF8String)(t,0,1e3)),calculate(t){const e=(0,r.toUTF8String)(t).match(s.root);if(e){const t=parseAttributes(e[0]);if(t.width&&t.height){return calculateByDimensions(t)}if(t.viewbox){return calculateByViewbox(t,t.viewbox)}}throw new TypeError("Invalid SVG")}}},753:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.TGA=void 0;const r=n(779);e.TGA={validate(t){return(0,r.readUInt16LE)(t,0)===0&&(0,r.readUInt16LE)(t,4)===0},calculate(t){return{height:(0,r.readUInt16LE)(t,14),width:(0,r.readUInt16LE)(t,12)}}}},940:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.TIFF=void 0;const r=n(147);const i=n(779);function readIFD(t,e,n){const s=(0,i.readUInt)(t,32,4,n);let o=1024;const c=r.statSync(e).size;if(s+o>c){o=c-s-10}const a=new Uint8Array(o);const u=r.openSync(e,"r");r.readSync(u,a,0,o,s);r.closeSync(u);return a.slice(2)}function readValue(t,e){const n=(0,i.readUInt)(t,16,8,e);const r=(0,i.readUInt)(t,16,10,e);return(r<<16)+n}function nextTag(t){if(t.length>24){return t.slice(12)}}function extractTags(t,e){const n={};let r=t;while(r&&r.length){const t=(0,i.readUInt)(r,16,0,e);const s=(0,i.readUInt)(r,16,2,e);const o=(0,i.readUInt)(r,32,4,e);if(t===0){break}else{if(o===1&&(s===3||s===4)){n[t]=readValue(r,e)}r=nextTag(r)}}return n}function determineEndianness(t){const e=(0,i.toUTF8String)(t,0,2);if("II"===e){return"LE"}else if("MM"===e){return"BE"}}const s=["49492a00","4d4d002a"];e.TIFF={validate:t=>s.includes((0,i.toHexString)(t,0,4)),calculate(t,e){if(!e){throw new TypeError("Tiff doesn't support buffer")}const n=determineEndianness(t)==="BE";const r=readIFD(t,e,n);const i=extractTags(r,n);const s=i[256];const o=i[257];if(!s||!o){throw new TypeError("Invalid Tiff. Missing tags")}return{height:o,width:s}}}},779:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.findBox=e.readUInt=e.readUInt32LE=e.readUInt32BE=e.readInt32LE=e.readUInt24LE=e.readUInt16LE=e.readUInt16BE=e.readInt16LE=e.toHexString=e.toUTF8String=void 0;const n=new TextDecoder;const toUTF8String=(t,e=0,r=t.length)=>n.decode(t.slice(e,r));e.toUTF8String=toUTF8String;const toHexString=(t,e=0,n=t.length)=>t.slice(e,n).reduce(((t,e)=>t+("0"+e.toString(16)).slice(-2)),"");e.toHexString=toHexString;const readInt16LE=(t,e=0)=>{const n=t[e]+t[e+1]*2**8;return n|(n&2**15)*131070};e.readInt16LE=readInt16LE;const readUInt16BE=(t,e=0)=>t[e]*2**8+t[e+1];e.readUInt16BE=readUInt16BE;const readUInt16LE=(t,e=0)=>t[e]+t[e+1]*2**8;e.readUInt16LE=readUInt16LE;const readUInt24LE=(t,e=0)=>t[e]+t[e+1]*2**8+t[e+2]*2**16;e.readUInt24LE=readUInt24LE;const readInt32LE=(t,e=0)=>t[e]+t[e+1]*2**8+t[e+2]*2**16+(t[e+3]<<24);e.readInt32LE=readInt32LE;const readUInt32BE=(t,e=0)=>t[e]*2**24+t[e+1]*2**16+t[e+2]*2**8+t[e+3];e.readUInt32BE=readUInt32BE;const readUInt32LE=(t,e=0)=>t[e]+t[e+1]*2**8+t[e+2]*2**16+t[e+3]*2**24;e.readUInt32LE=readUInt32LE;const r={readUInt16BE:e.readUInt16BE,readUInt16LE:e.readUInt16LE,readUInt32BE:e.readUInt32BE,readUInt32LE:e.readUInt32LE};function readUInt(t,e,n,i){n=n||0;const s=i?"BE":"LE";const o="readUInt"+e+s;return r[o](t,n)}e.readUInt=readUInt;function readBox(t,n){if(t.length-n<4)return;const r=(0,e.readUInt32BE)(t,n);if(t.length-n<r)return;return{name:(0,e.toUTF8String)(t,4+n,8+n),offset:n,size:r}}function findBox(t,e,n){while(n<t.length){const r=readBox(t,n);if(!r)break;if(r.name===e)return r;n+=r.size>0?r.size:8}}e.findBox=findBox},226:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.WEBP=void 0;const r=n(779);function calculateExtended(t){return{height:1+(0,r.readUInt24LE)(t,7),width:1+(0,r.readUInt24LE)(t,4)}}function calculateLossless(t){return{height:1+((t[4]&15)<<10|t[3]<<2|(t[2]&192)>>6),width:1+((t[2]&63)<<8|t[1])}}function calculateLossy(t){return{height:(0,r.readInt16LE)(t,8)&16383,width:(0,r.readInt16LE)(t,6)&16383}}e.WEBP={validate(t){const e="RIFF"===(0,r.toUTF8String)(t,0,4);const n="WEBP"===(0,r.toUTF8String)(t,8,12);const i="VP8"===(0,r.toUTF8String)(t,12,15);return e&&n&&i},calculate(t){const e=(0,r.toUTF8String)(t,12,16);t=t.slice(20,30);if(e==="VP8X"){const e=t[0];const n=(e&192)===0;const r=(e&1)===0;if(n&&r){return calculateExtended(t)}else{throw new TypeError("Invalid WebP")}}if(e==="VP8 "&&t[0]!==47){return calculateLossy(t)}const n=(0,r.toHexString)(t,3,6);if(e==="VP8L"&&n!=="9d012a"){return calculateLossless(t)}throw new TypeError("Invalid WebP")}}},112:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.BitReader=void 0;class BitReader{constructor(t,e){this.input=t;this.endianness=e;this.byteOffset=2;this.bitOffset=0}getBits(t=1){let e=0;let n=0;while(n<t){if(this.byteOffset>=this.input.length){throw new Error("Reached end of input")}const r=this.input[this.byteOffset];const i=8-this.bitOffset;const s=Math.min(t-n,i);if(this.endianness==="little-endian"){const t=(1<<s)-1;const i=r>>this.bitOffset&t;e|=i<<n}else{const t=(1<<s)-1<<8-this.bitOffset-s;const n=(r&t)>>8-this.bitOffset-s;e=e<<s|n}n+=s;this.bitOffset+=s;if(this.bitOffset===8){this.byteOffset++;this.bitOffset=0}}return e}}e.BitReader=BitReader},842:(t,e,n)=>{try{var r=n(837);if(typeof r.inherits!=="function")throw"";t.exports=r.inherits}catch(e){t.exports=n(782)}},782:t=>{if(typeof Object.create==="function"){t.exports=function inherits(t,e){if(e){t.super_=e;t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:false,writable:true,configurable:true}})}}}else{t.exports=function inherits(t,e){if(e){t.super_=e;var TempCtor=function(){};TempCtor.prototype=e.prototype;t.prototype=new TempCtor;t.prototype.constructor=t}}}},927:(t,e,n)=>{var r=n(842);var i=n(361).EventEmitter;t.exports=Queue;t.exports["default"]=Queue;function Queue(t){if(!(this instanceof Queue)){return new Queue(t)}i.call(this);t=t||{};this.concurrency=t.concurrency||Infinity;this.timeout=t.timeout||0;this.autostart=t.autostart||false;this.results=t.results||null;this.pending=0;this.session=0;this.running=false;this.jobs=[];this.timers={}}r(Queue,i);var s=["pop","shift","indexOf","lastIndexOf"];s.forEach((function(t){Queue.prototype[t]=function(){return Array.prototype[t].apply(this.jobs,arguments)}}));Queue.prototype.slice=function(t,e){this.jobs=this.jobs.slice(t,e);return this};Queue.prototype.reverse=function(){this.jobs.reverse();return this};var o=["push","unshift","splice"];o.forEach((function(t){Queue.prototype[t]=function(){var e=Array.prototype[t].apply(this.jobs,arguments);if(this.autostart){this.start()}return e}}));Object.defineProperty(Queue.prototype,"length",{get:function(){return this.pending+this.jobs.length}});Queue.prototype.start=function(t){if(t){callOnErrorOrEnd.call(this,t)}this.running=true;if(this.pending>=this.concurrency){return}if(this.jobs.length===0){if(this.pending===0){done.call(this)}return}var e=this;var n=this.jobs.shift();var r=true;var i=this.session;var s=null;var o=false;var c=null;var a=n.hasOwnProperty("timeout")?n.timeout:this.timeout;function next(t,a){if(r&&e.session===i){r=false;e.pending--;if(s!==null){delete e.timers[s];clearTimeout(s)}if(t){e.emit("error",t,n)}else if(o===false){if(c!==null){e.results[c]=Array.prototype.slice.call(arguments,1)}e.emit("success",a,n)}if(e.session===i){if(e.pending===0&&e.jobs.length===0){done.call(e)}else if(e.running){e.start()}}}}if(a){s=setTimeout((function(){o=true;if(e.listeners("timeout").length>0){e.emit("timeout",next,n)}else{next()}}),a);this.timers[s]=s}if(this.results){c=this.results.length;this.results[c]=null}this.pending++;e.emit("start",n);var u=n(next);if(u&&u.then&&typeof u.then==="function"){u.then((function(t){return next(null,t)})).catch((function(t){return next(t||true)}))}if(this.running&&this.jobs.length>0){this.start()}};Queue.prototype.stop=function(){this.running=false};Queue.prototype.end=function(t){clearTimers.call(this);this.jobs.length=0;this.pending=0;done.call(this,t)};function clearTimers(){for(var t in this.timers){var e=this.timers[t];delete this.timers[t];clearTimeout(e)}}function callOnErrorOrEnd(t){var e=this;this.on("error",onerror);this.on("end",onend);function onerror(t){e.end(t)}function onend(n){e.removeListener("error",onerror);e.removeListener("end",onend);t(n,this.results)}}function done(t){this.session++;this.running=false;this.emit("end",t)}},361:t=>{"use strict";t.exports=require("events")},147:t=>{"use strict";t.exports=require("fs")},17:t=>{"use strict";t.exports=require("path")},837:t=>{"use strict";t.exports=require("util")}};var e={};function __nccwpck_require__(n){var r=e[n];if(r!==undefined){return r.exports}var i=e[n]={exports:{}};var s=true;try{t[n](i,i.exports,__nccwpck_require__);s=false}finally{if(s)delete e[n]}return i.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(833);module.exports=n})();